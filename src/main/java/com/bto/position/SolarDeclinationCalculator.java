package com.bto.position;

/**
 * <AUTHOR>
 */
public class SolarDeclinationCalculator {

    // 计算太阳赤纬（单位：弧度）  
    public static double calculateSolarDeclination(double julianDay) {
        // 计算从J2000.0（2000年1月1日12:00 TT）起的天数
        double n = julianDay - 2451545.0;

        // 计算太阳的平均经度（单位：度）
        // 这里使用了取余操作来确保角度在0到360度之间，并加上360度再取余以防止结果为负
        double lDegrees = (280.460 + 0.9856474 * n) % 360;
        if (lDegrees < 0) {
            lDegrees += 360; // 确保L_degrees是非负的
        }

        // 将平均经度转换为弧度
        double l = Math.toRadians(lDegrees);

        // 计算太阳的赤纬（单位：弧度）

        return Math.asin(Math.sin(Math.toRadians(23.439)) * Math.sin(l));
    }

    public static void main(String[] args) {
        // 示例：计算某个儒略日对应的太阳赤纬
        double julianDayExample = 2460386;

        // 计算太阳赤纬（单位：弧度）
        double solarDeclination = calculateSolarDeclination(julianDayExample);

        // 将弧度转换为度
        double solarDeclinationDegrees = Math.toDegrees(solarDeclination);

        // 输出结果
        System.out.println("太阳赤纬（弧度）: " + solarDeclination);
        System.out.println("太阳赤纬（度）: " + solarDeclinationDegrees);
    }
}