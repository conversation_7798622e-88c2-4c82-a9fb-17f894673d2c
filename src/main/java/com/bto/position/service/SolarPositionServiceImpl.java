package com.bto.position.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.position.mapper.PlantMapper;
import com.bto.position.mapper.SolarPositionMapper;
import com.bto.position.pojo.Plant;
import com.bto.position.pojo.SolarPosition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/3/18.
 */
@Service
public class SolarPositionServiceImpl extends ServiceImpl<SolarPositionMapper, SolarPosition> implements SolarPositionService {
    @Override
    @Transactional
    public void saveSolarPosition(List<SolarPosition> solarPositions) {
        this.saveBatch(solarPositions);

    }
}
