package com.bto.position.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.position.mapper.PlantMapper;
import com.bto.position.mapper.SolarPositionMapper;
import com.bto.position.pojo.Plant;
import com.bto.position.pojo.SolarPosition;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/3/14.
 */

@Service
@AllArgsConstructor
public class PlantServiceImpl extends ServiceImpl<PlantMapper, Plant> implements PlantService {

    private final SolarPositionMapper solarPositionMapper;

    @Override
    public List<Plant> getPlantList() {
        ArrayList<String> list = new ArrayList<>();
        // list.add("0342F88B-F74C-4766-A156-25E4E68C9AE0");
        // list.add("2ECEB23E-4F7C-46B4-B6FE-0BBAA298DB52");
        // list.add("0974A5E2-7770-499A-A0CD-38DCEEFFF63C");
        // list.add("EAB6F848-124C-4D52-B177-ABC31D30257F");
        // list.add("084FC889-5345-4704-B06E-E567353456B4");
        // list.add("C724D5CB-0CAF-4354-B37B-C9F413EED825");
        LambdaQueryWrapper<Plant> queryWrap = new LambdaQueryWrapper<>();
        // queryWrap.in(Plant::getPlantUid, list);
        queryWrap.select(Plant::getPlantUid, Plant::getLongitude, Plant::getLatitude, Plant::getBankAngle, Plant::getAzimuth);
        return baseMapper.selectList(queryWrap);
    }

}