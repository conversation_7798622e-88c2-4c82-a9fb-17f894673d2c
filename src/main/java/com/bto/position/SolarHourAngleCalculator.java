package com.bto.position;

/**
 * <AUTHOR>
 */
public class SolarHourAngleCalculator {

    // 计算时差（单位：小时）  
    private static double calculateTimeDifference(double localLongitude) {
        double longitudeDifference = localLongitude - 120.0; // 经度差，以北京时间为基准  
        double timeDifferenceHours = longitudeDifference / 15.0; // 时差，每小时相当于15度经度  
        return timeDifferenceHours;
    }

    // 计算真太阳时（单位：小时）  
    private static double calculateTrueSolarTime(double beijingTime, double timeDifference) {
        double trueSolarTime = beijingTime + timeDifference; // 真太阳时 = 北京时间 + 时差  
        // 如果真太阳时超过24小时，则减去24小时，使其回到0-24小时范围内  
        if (trueSolarTime >= 24.0) {
            trueSolarTime -= 24.0;
        }
        return trueSolarTime;
    }

    // 计算太阳时角（单位：度）  
    public static double calculateSolarHourAngle(double beijingTime, double localLongitude) {
        double timeDifference = calculateTimeDifference(localLongitude); // 计算时差  
        double trueSolarTime = calculateTrueSolarTime(beijingTime, timeDifference); // 计算真太阳时  
        // 计算太阳时角
        return 15.0 * (trueSolarTime - 12.0);
    }

    public static void main(String[] args) {
        // 示例：北京时间 = 14点，当地经度 = 116°（以北京为例）  
        double beijingTime = 14.0; // 北京时间，以24小时制表示
        double localLongitude = 115.78388; // 当地经度，以度为单位

        // 计算太阳时角  
        double solarHourAngle = calculateSolarHourAngle(beijingTime, localLongitude);

        // 输出结果  
        System.out.printf("太阳时角（度）: " + solarHourAngle);
    }
}