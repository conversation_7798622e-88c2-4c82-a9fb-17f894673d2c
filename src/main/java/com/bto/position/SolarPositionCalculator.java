package com.bto.position;

import org.hipparchus.geometry.euclidean.threed.Vector3D;
import org.orekit.bodies.*;
import org.orekit.data.DataContext;
import org.orekit.data.DirectoryCrawler;
import org.orekit.frames.Frame;
import org.orekit.frames.FramesFactory;
import org.orekit.frames.TopocentricFrame;
import org.orekit.time.AbsoluteDate;
import org.orekit.time.DateComponents;
import org.orekit.time.TimeComponents;
import org.orekit.time.TimeScalesFactory;
import org.orekit.utils.Constants;
import org.orekit.utils.IERSConventions;
import org.orekit.utils.PVCoordinates;

import java.io.File;

/**
 * <AUTHOR>
 */
public class SolarPositionCalculator {

    public static void main(String[] args) {

        // 设置时间和地点
        int year = 2024;
        int month = 3;
        int day = 16;
        int hour = 15;
        int minute = 48;
        int second = 0;
        // 纬度
        double latitude = 40.34924;
        // 经度
        double longitude = 115.78388;
        // 海拔，单位为米
        double altitude = 0.0;

        File orekitData = new File("D:\\IdeaProjects\\Weather\\src\\main\\resources\\orekit-data");
        DataContext.getDefault().getDataProvidersManager().addProvider(new DirectoryCrawler(orekitData));

        // 创建时间对象
        AbsoluteDate date = new AbsoluteDate(
                new DateComponents(year, month, day),
                new TimeComponents(hour, minute, second),
                TimeScalesFactory.getUTC());

        // 获取地心地固坐标系（ECEF）
        Frame itrfFrame = FramesFactory.getITRF(IERSConventions.IERS_2010, true);

        // 获取太阳位置（在地心惯性坐标系中）然后转换到ECEF
        CelestialBody sun = CelestialBodyFactory.getSun();
        PVCoordinates sunPV = sun.getPVCoordinates(date, itrfFrame);
        Vector3D sunPositionEcef = sunPV.getPosition();

        // 创建观测点的地理坐标
        GeodeticPoint observerPoint = new GeodeticPoint(latitude, longitude, altitude);
        BodyShape bodyShape = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,
                Constants.WGS84_EARTH_FLATTENING,
                itrfFrame);

        // 创建以观察者为中心的局部坐标系（ENU）
        TopocentricFrame observerFrame = new TopocentricFrame(bodyShape, observerPoint, "station");

        // 将太阳位置从ECEF转换到观察者局部坐标系（ENU）
        Vector3D sunPositionEnu = observerFrame.getTransformTo(itrfFrame, date).transformVector(sunPositionEcef);


        // 方位角
        double azimuthAngle = Math.toDegrees(Math.atan2(sunPositionEnu.getY(), sunPositionEnu.getX()));
        if (azimuthAngle < 0) {
            // 保证方位角在0到360度之间
            azimuthAngle += 360;
        }

        // 高度角
        double elevationAngle = Math.toDegrees(Math.asin(sunPositionEnu.getZ() / sunPositionEnu.getNorm()));

        // 输出结果
        System.out.println("太阳高度角：" + elevationAngle + "°");
        System.out.println("太阳方位角：" + azimuthAngle + "°");

    }


}