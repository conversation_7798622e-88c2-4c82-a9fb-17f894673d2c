package com.bto.position;

/**
 * <AUTHOR>
 */
public class JulianDayCalculator {

    // 常数，用于儒略日计算  
    private static final int JULIAN_DAY_OFFSET = 2440588; // 1970年1月1日的儒略日数  
    private static final int[] MONTH_DAYS_NORMAL = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 判断是否为闰年  
    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    // 获取指定年份和月份的天数  
    private static int getDaysInMonth(int year, int month) {
        if (month == 2) {
            return isLeapYear(year) ? 29 : 28;
        }
        return MONTH_DAYS_NORMAL[month - 1];
    }

    // 计算从Unix纪元（1970年1月1日）到指定日期的总天数  
    private static int daysSinceEpoch(int year, int month, int day) {
        int days = 0;
        for (int y = 1970; y < year; y++) {
            days += isLeapYear(y) ? 366 : 365;
        }
        for (int m = 1; m < month; m++) {
            days += getDaysInMonth(year, m);
        }
        days += day - 1; // 减去1因为1970年1月1日已经是第1天  
        return days;
    }

    // 计算儒略日
    public static double calculateJulianDay(int year, int month, int day, int hour, int minute, int second) {
        int days = daysSinceEpoch(year, month, day);
        // 将时间转换为儒略日的小数部分（假设一天有86400秒）
        double fractionOfDay = (hour * 3600 + minute * 60 + second) / 86400.0;
        // 将总天数与小数部分相加，然后四舍五入到最近的整数
        return JULIAN_DAY_OFFSET + days + Math.round(fractionOfDay);
    }

    public static void main(String[] args) {
        double julianDay = calculateJulianDay(2024, 3, 16, 11, 24, 0);
        System.out.println("儒略日（Julian Day）是: " + julianDay);
    }

}