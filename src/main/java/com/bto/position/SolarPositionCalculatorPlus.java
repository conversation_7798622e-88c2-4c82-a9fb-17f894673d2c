package com.bto.position;

import com.bto.position.pojo.CalculationParameter;
import com.bto.position.pojo.SolarPosition;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SolarPositionCalculatorPlus {

    // 计算太阳高度角（单位：弧度）
    public static double calculateSolarHeightAngle(double latitude, double solarDeclination, double solarHourAngle) {
        double latRad = Math.toRadians(latitude);
        double sinLatitude = Math.sin(latRad);
        double cosLatitude = Math.cos(latRad);

        double solarHeightAngleRadians = Math.asin(sinLatitude * Math.sin(solarDeclination) +
                cosLatitude * Math.cos(solarDeclination) * Math.cos(solarHourAngle));
        return solarHeightAngleRadians;
    }

    public static double calculateSolarAzimuth(double latitude, double solarDeclination, double hourAngle) {
        double cosLatitude = Math.cos(latitude);
        double sinLatitude = Math.sin(latitude);
        double cosDeclination = Math.cos(solarDeclination);
        double sinDeclination = Math.sin(solarDeclination);
        double cosHourAngle = Math.cos(hourAngle);
        double sinHourAngle = Math.sin(hourAngle);

        // 计算太阳高度角的余弦值
        double cosSolarAltitude = sinLatitude * sinDeclination + cosLatitude * cosDeclination * cosHourAngle;

        // 确保太阳高度角的余弦值在有效范围内
        if (cosSolarAltitude > 1) {
            cosSolarAltitude = 1;
        } else if (cosSolarAltitude < -1) {
            cosSolarAltitude = -1;
        }

        // 计算太阳方位角的余弦值（使用第二个公式，因为它看起来更常见和可靠）
        double cosAzimuth = (sinDeclination - sinLatitude * cosSolarAltitude) / (cosLatitude * Math.sqrt(1 - cosSolarAltitude * cosSolarAltitude));

        // 确保太阳方位角的余弦值在有效范围内
        if (cosAzimuth > 1) {
            cosAzimuth = 1;
        } else if (cosAzimuth < -1) {
            cosAzimuth = -1;
        }

        // 计算太阳方位角（弧度）
        double azimuth = Math.acos(cosAzimuth);

        // 根据时角修正方位角以确保正确的象限
        if (hourAngle > 0) { // 下午，方位角应该在180到360度之间
            azimuth = 2 * Math.PI - azimuth;
        }

        // 将方位角转换为度
        return Math.toDegrees(azimuth);
    }

    // 将角度转换为弧度
    private static double toRadians(double angleInDegrees) {
        return Math.toRadians(angleInDegrees);
    }

    // 将弧度转换为角度
    private static double toDegrees(double angleInRadians) {
        return Math.toDegrees(angleInRadians);
    }


    public static List<CalculationParameter> getTodayHourlyParameters() {
        String timeZoneId = "Asia/Shanghai";
        List<CalculationParameter> parameters = new ArrayList<>();
        // 获取当前日期时间的ZonedDateTime实例
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timeZoneId));
        // 获取昨天的时间
        // now = now.minusDays(1);
        // 获取明天的时间
        now = now.plusDays(1);
        int year = now.getYear();
        int month = now.getMonthValue();
        int day = now.getDayOfMonth();

        // 遍历24小时，为每个小时创建一个CalculationParameter实例
        for (int hour = 5; hour <= 20; hour++) {
            LocalDateTime dateTime = LocalDateTime.of(year, month, day, hour, 0, 0);
            ZonedDateTime zonedDateTime = dateTime.atZone(ZoneId.of(timeZoneId));
            Double timeZoneOffset = zonedDateTime.getOffset().getTotalSeconds() / 3600.0;

            CalculationParameter param = new CalculationParameter(
                    year, month, day, hour, 0, 0, timeZoneOffset, dateTime
            );
            parameters.add(param);
        }
        return parameters;
    }

    public static SolarPosition getSolarPosition(CalculationParameter calculationParameter) {
        Integer year = calculationParameter.getYear();
        Integer month = calculationParameter.getMonth();
        Integer day = calculationParameter.getDay();
        Integer hour = calculationParameter.getHour();
        Integer minute = calculationParameter.getMinute();
        Integer second = calculationParameter.getSecond();
        Double latitude = calculationParameter.getLatitude();
        Double longitude = calculationParameter.getLongitude();
        // 儒略日
        double julianDay = JulianDayCalculator.calculateJulianDay(year, month, day, hour, minute, second);
        // 太阳赤纬（弧度)
        double solarDeclination = SolarDeclinationCalculator.calculateSolarDeclination(julianDay);
        // 太阳时角（单位：弧度）
        double solarHourAngle = SolarHourAngleCalculator.calculateSolarHourAngle(hour, longitude);
        solarHourAngle = solarHourAngle * (Math.PI / 180);
        // 计算太阳高度角（单位：弧度）
        double solarHeightAngleRadians = calculateSolarHeightAngle(latitude, solarDeclination, solarHourAngle);
        // 将弧度转换为度
        double solarHeightAngleDegrees = Math.toDegrees(solarHeightAngleRadians);
        // 计算太阳方位角（单位：度）
        // double azimuthAngle = calculateSolarAzimuth(latitude, solarDeclination, solarHourAngle);
        double azimuthAngle = calculateSolarAzimuthss(latitude, solarDeclination, solarHourAngle);

        return new SolarPosition(azimuthAngle, solarHeightAngleDegrees);
    }

    /**
     * 计算太阳方位角
     * @param latitude 观测地点的纬度（单位：弧度）
     * @param declination 太阳赤纬（单位：弧度）
     * @param hourAngle 太阳时角（单位：弧度）
     * @return 太阳方位角（单位：弧度）
     */
    public static double calculateSolarAzimuthss(double latitude, double declination, double hourAngle) {

        latitude = toRadians(latitude);

        hourAngle = toRadians(hourAngle);

        // 计算太阳方位角（使用球面三角学公式）
        double cosAzimuth = (Math.sin(declination) - Math.sin(latitude) * Math.sin(getSolarElevation(latitude, declination, hourAngle))) /
                (Math.cos(latitude) * Math.cos(getSolarElevation(latitude, declination, hourAngle)));

        // 因为cos函数的值域是[-1, 1]，所以需要对cosAzimuth进行范围检查，避免数值误差导致的异常
        if (cosAzimuth > 1) {
            cosAzimuth = 1;
        } else if (cosAzimuth < -1) {
            cosAzimuth = -1;
        }

        // 计算方位角，并转换为[0, 2π]范围
        double azimuth = Math.acos(cosAzimuth);
        if (hourAngle > 0) {
            azimuth = 2 * Math.PI - azimuth; // 如果太阳时角大于0，则太阳在西方，方位角需要调整为大于π的值
        }

        double degrees = toDegrees(azimuth);

        return degrees;
    }

    /**
     * 计算太阳高度角（辅助方法）
     * @param latitude 观测地点的纬度（单位：弧度）
     * @param declination 太阳赤纬（单位：弧度）
     * @param hourAngle 太阳时角（单位：弧度）
     * @return 太阳高度角（单位：弧度）
     */
    private static double getSolarElevation(double latitude, double declination, double hourAngle) {
        return Math.asin(Math.sin(latitude) * Math.sin(declination) + Math.cos(latitude) * Math.cos(declination) * Math.cos(hourAngle));
    }

    public static void main(String[] args) {
        // 儒略日
        double julianDay = JulianDayCalculator.calculateJulianDay(2024, 3, 16, 15, 56, 0);
        // 太阳赤纬（弧度)
        double solarDeclination = SolarDeclinationCalculator.calculateSolarDeclination(julianDay);
        // 太阳时角（单位：弧度）
        double solarHourAngle = SolarHourAngleCalculator.calculateSolarHourAngle(15, 115.78388);
        solarHourAngle = solarHourAngle * (Math.PI / 180);

        // 将弧度转换为度
        double latitude = 40.34924; // 纬度（度）

        // 计算太阳高度角（单位：弧度）
        double solarHeightAngleRadians = calculateSolarHeightAngle(latitude, solarDeclination, solarHourAngle);

        // 计算太阳方位角（单位：弧度）
        double azimuthAngle = calculateSolarAzimuth(latitude, solarDeclination, solarHourAngle);

        // 将弧度转换为度
        double solarHeightAngleDegrees = Math.toDegrees(solarHeightAngleRadians);
        double v = calculateSolarAzimuthss(latitude, solarDeclination, solarHourAngle);
        // 输出结果
        System.out.println("太阳高度角（度）: " + solarHeightAngleDegrees);
        System.out.println("太阳方位角（度）: " + azimuthAngle+"v:"+v);
    }


}