package com.bto.position.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 电站数据
 */
@Data
@TableName("v_angle_parameter")
public class Plant implements Serializable {

    /**
     * 电站Uid
     */
    @TableField(value = "plant_uid")
    private String plantUid;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private String longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private String latitude;

    /**
     * 倾斜角度
     */
    @TableField(value = "bank_angle")
    private Integer bankAngle;

    /**
     * 方位
     */
    @TableField(value = "azimuth")
    private Integer azimuth;

}