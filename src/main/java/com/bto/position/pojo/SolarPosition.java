package com.bto.position.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("bto_plant_ks")
public class SolarPosition {

    private String plantUid;

    private double bankAngle;

    private double azimuth;

    // 方位角（度）
    private double solarAzimuth;

    // 高度角（度）
    private double solarHeight;

    private Double ks1;
    private Double ks2;

    private LocalDateTime createTime;

    public SolarPosition(double solarAzimuth, double solarHeight) {
        this.solarAzimuth = solarAzimuth;
        this.solarHeight = solarHeight;
    }
}