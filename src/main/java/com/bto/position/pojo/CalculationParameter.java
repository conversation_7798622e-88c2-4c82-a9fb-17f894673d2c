package com.bto.position.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> by zhb on 2024/3/13.
 */
@Data
public class CalculationParameter {

    // 纬度
    private Double latitude;
    // 经度
    private Double longitude;
    // 年份
    private Integer year;
    // 月份
    private Integer month;
    // 日期
    private Integer day;
    // 小时
    private Integer hour;
    // 分钟
    private Integer minute;
    // 秒
    private Integer second;
    // 时区
    private Double timeZone;

    private LocalDateTime nowDateTime;

    public CalculationParameter(Integer year, Integer month, Integer day, Integer hour, Integer minute, Integer second, Double timeZone,LocalDateTime nowDateTime) {
        this.year = year;
        this.month = month;
        this.day = day;
        this.hour = hour;
        this.minute = minute;
        this.second = second;
        this.timeZone = timeZone.doubleValue();
        this.nowDateTime = nowDateTime;
    }

}