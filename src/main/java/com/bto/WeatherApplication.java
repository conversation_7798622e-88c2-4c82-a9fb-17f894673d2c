package com.bto;

import cn.hutool.cron.CronUtil;
import com.bto.position.service.PlantService;
import com.bto.position.service.SolarPositionService;
import com.bto.task.BtoTimerTask;
import lombok.AllArgsConstructor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.bto.*.mapper")
@AllArgsConstructor
public class WeatherApplication implements CommandLineRunner {


    private final PlantService plantService;

    private final SolarPositionService solarPositionService;

    public static void main(String[] args) {
        SpringApplication.run(WeatherApplication.class, args);
    }

    @Override
    public void run(String... args) {
        CronUtil.start();
        BtoTimerTask.runWeather();
        BtoTimerTask.runSolarCalculator(plantService,solarPositionService);
    }

}