package com.bto.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import cn.hutool.http.HttpUtil;
import com.bto.position.SolarPositionCalculatorPlus;
import com.bto.position.SunAngleCalculator;
import com.bto.position.pojo.CalculationParameter;
import com.bto.position.pojo.Plant;
import com.bto.position.pojo.SolarPosition;
import com.bto.position.service.PlantService;
import com.bto.position.service.SolarPositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BtoTimerTask {

    public static void runWeather() {
        // 支持秒级别定时任务
        CronUtil.setMatchSecond(true);
        // 每天凌晨30分、4点30分、8点30分、12点30分、16点30分、20点30分
        CronUtil.schedule("30 0,4,8,12,16,20 * * * ", new Task() {
            @Override
            public void execute() {
                // http://127.0.0.1:9999/getCityWeather?city=广州市
                String msg = HttpUtil.get("http://127.0.0.1:9999/getWeather");
                log.info("当前时间：" + DateUtil.now() + "，天气数据为：" + msg);
            }
        });
    }

    public static void runSolarCalculator(PlantService plantService, SolarPositionService solarPositionService) {
        CronUtil.setMatchSecond(true);
        CronUtil.schedule("30 23 * * *", new Task() {
            @Override
            public void execute() {
                List<Plant> plantList = plantService.getPlantList();
                for (Plant plant : plantList) {
                    List<CalculationParameter> todayHourlyParameters = SolarPositionCalculatorPlus.getTodayHourlyParameters();
                    ArrayList<SolarPosition> solarPositions = new ArrayList<>();
                    todayHourlyParameters.forEach(calculationParameter -> {
                        calculationParameter.setLongitude(Double.parseDouble(plant.getLongitude()));
                        calculationParameter.setLatitude(Double.parseDouble(plant.getLatitude()));

                        SolarPosition solarPosition = SunAngleCalculator.getSolarPosition(calculationParameter);

                        solarPosition.setCreateTime(calculationParameter.getNowDateTime());
                        solarPosition.setPlantUid(plant.getPlantUid());
                        HashMap<String, Double> hashMap = calculateKs(solarPosition.getSolarAzimuth(), solarPosition.getSolarHeight(), plant.getAzimuth(), plant.getBankAngle());

                        solarPosition.setBankAngle(plant.getBankAngle());
                        solarPosition.setAzimuth(plant.getAzimuth());
                        solarPosition.setKs1(hashMap.get("ks1"));
                        solarPosition.setKs2(hashMap.get("ks2"));
                        solarPositions.add(solarPosition);
                    });
                    solarPositionService.saveSolarPosition(solarPositions);
                    solarPositions.clear();
                }
            }
        });
    }


    /**
     * 根据提供的公式计算ks系数
     * KS=cosθ/Sinh=cosβ+Sinβ·cos(Ψ-α) /tanh
     * h是太阳高度角，Ψ是太阳的方位角，β固定为5度，α为组件方位角减去180度
     */
    public static HashMap<String, Double> calculateKs(double solarAzimuthAngle, double solarAltitudeAngle, double azimuth, double bankAngle) {
        HashMap<String, Double> hashMap = new HashMap<>();
        // 光伏板组件方位角减去180度
        double ks1_alpha = azimuth - 180;
        double ks2_alpha = 0.0;
        if (azimuth < 180) {
            ks2_alpha = azimuth;
        } else {
            ks2_alpha = azimuth - 360;
        }

        double beta = bankAngle;

        // 将角度转换为弧度并计算正切值
        double tanH = Math.tan(Math.toRadians(solarAltitudeAngle));
        // 防止除以0的情况（当太阳正好在地平线上时，tan(h)接近0）
        if (tanH == 0) {
            hashMap.put("ks1", 0.0);
            hashMap.put("ks2", 0.0);
            return hashMap; // 或者可以返回一个错误值或抛出异常
        }

        // 计算ks系数
        double ks1 = Math.cos(Math.toRadians(beta)) + Math.sin(Math.toRadians(beta)) * Math.cos(Math.toRadians(solarAzimuthAngle - ks1_alpha)) / tanH;
        double ks2 = Math.cos(Math.toRadians(beta)) + Math.sin(Math.toRadians(beta)) * Math.cos(Math.toRadians(solarAzimuthAngle - ks2_alpha)) / tanH;

        hashMap.put("ks1", ks1);
        hashMap.put("ks2", ks2);
        return hashMap;
    }


}