package com.bto.weather.controller;

import com.bto.weather.pojo.CityInfoDTO;
import com.bto.weather.pojo.WeatherMsg;
import com.bto.weather.service.SetWeatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class SpringRestTemplateController {

    @Autowired
    private SetWeatherService setWeatherService;

    @GetMapping("/getWeather")
    public String get() {
        ArrayList<CityInfoDTO> cityInfos = setWeatherService.selectAllCityInfo();
        String console = "";
        if (cityInfos != null) {
            console = setWeatherService.handleWeatherInfo(cityInfos);
        } else {
            return "查不到天气数据！请检查接口是否异常！";
        }
        return console;
    }

    @GetMapping("getCityWeather")
    public List<WeatherMsg> getCityWeather(String city) {
        return setWeatherService.getCityWeather(city);
    }
}