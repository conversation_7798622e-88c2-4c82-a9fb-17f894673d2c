package com.bto.weather.service;

import com.bto.weather.pojo.CityInfoDTO;
import com.bto.weather.pojo.WeatherMsg;

import java.util.ArrayList;
import java.util.List;

public interface SetWeatherService {

    /**
     * 处理数据并存入数据库
     */
    String handleWeatherInfo(ArrayList<CityInfoDTO> locations);

    /**
     * 查询所有城市的名称和坐标信息
     */
    ArrayList<CityInfoDTO> selectAllCityInfo();

    /**
     * 根据城市的名称查询天气
     */
    List<WeatherMsg> getCityWeather(String city);

}