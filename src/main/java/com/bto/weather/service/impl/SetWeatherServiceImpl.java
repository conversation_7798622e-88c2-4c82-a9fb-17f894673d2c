package com.bto.weather.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.bto.weather.mapper.SetWeatherMapper;
import com.bto.weather.pojo.CityInfoDTO;
import com.bto.weather.pojo.WeatherMsg;
import com.bto.weather.service.SetWeatherService;
import net.i2p.crypto.eddsa.EdDSAEngine;
import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class SetWeatherServiceImpl implements SetWeatherService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private SetWeatherMapper setWeatherMapper;

    @Override
    public String handleWeatherInfo(ArrayList<CityInfoDTO> cityInfos) {
        int insertTotal = 0;
        int updateTotal = 0;
        for (CityInfoDTO cityInfo : cityInfos) {
            String location = cityInfo.getLocation();
            String uri = "https://k83md7w287.yun.qweatherapi.com/v7/weather/24h?location=" + location;
            HttpResponse response = HttpRequest.get(uri)
                    .header("Authorization", "Bearer " + getJwt())
                    .execute();

            // 先获取响应体
            String strBody = response.body();
            JSONObject jsonObject = JSONObject.parseObject(strBody);
            Object hourly = jsonObject.get("hourly");
            // 转为实体数组
            List<WeatherMsg> next24HoursWeather = JSONObject.parseArray(hourly.toString(), WeatherMsg.class);
            for (WeatherMsg weatherMsg : next24HoursWeather) {
                String time = weatherMsg.getFxTime().substring(0, 16).replace("T", " ");
                String city = cityInfo.getCity();
                String cloud = weatherMsg.getCloud();
                String dew = weatherMsg.getDew();
                String humidity = weatherMsg.getHumidity();
                String precip = weatherMsg.getPrecip();
                String pressure = weatherMsg.getPressure();
                String wind360 = weatherMsg.getWind360();
                String weather = weatherMsg.getText();
                String temp = weatherMsg.getTemp();
                String windDir = weatherMsg.getWindDir();
                String windScale = weatherMsg.getWindScale();
                String windSpeed = weatherMsg.getWindSpeed();
                // 解析带时区的字符串到ZonedDateTime
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(weatherMsg.getFxTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                Date date = Date.from(zonedDateTime.toInstant());
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                SimpleDateFormat month = new SimpleDateFormat("yyyyMM");
                String currentTime = simpleDateFormat.format(new Date());
                String monthString = month.format(date);
                int count = setWeatherMapper.selectRepeatData(monthString, time, city);
                if (count <= 0) {
                    int insertCount = setWeatherMapper.insertAll(monthString, city, time, weather, temp, windDir, windScale, windSpeed,
                            humidity, precip, pressure, cloud, dew, currentTime, wind360);
                    insertTotal += insertCount;
                } else {
                    int updateCount = setWeatherMapper.updateRepeatData(monthString, city, time, weather, temp, windDir, windScale, windSpeed,
                            humidity, precip, pressure, cloud, dew, currentTime, wind360);
                    updateTotal += updateCount;
                }
            }
        }
        return "数据更新了：" + updateTotal + "条数据，插入了：" + insertTotal + "条数据！";
    }

    public static String getJwt() {
        try {
            // Private key
            String privateKeyString = "MC4CAQAwBQYDK2VwBCIEIEZpBD6tTt4pJZAmr1tx+aVvdVxJu5Mf2J0wXwdkjt7G";
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyString);
            PKCS8EncodedKeySpec encoded = new PKCS8EncodedKeySpec(privateKeyBytes);
            PrivateKey privateKey = new EdDSAPrivateKey(encoded);

            // Header
            String headerJson = "{\"alg\": \"EdDSA\", \"kid\": \"CEPPBBJ6VC\"}";

            // Payload
            long iat = ZonedDateTime.now(ZoneOffset.UTC).toEpochSecond() - 30;
            long exp = iat + 900;
            String payloadJson = "{\"sub\": \"3CTM7GAHA9\", \"iat\": " + iat + ", \"exp\": " + exp + "}";

            // Base64url header+payload
            String headerEncoded = Base64.getUrlEncoder().encodeToString(headerJson.getBytes(StandardCharsets.UTF_8));
            String payloadEncoded = Base64.getUrlEncoder().encodeToString(payloadJson.getBytes(StandardCharsets.UTF_8));
            String data = headerEncoded + "." + payloadEncoded;

            EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName(EdDSANamedCurveTable.ED_25519);

            // Sign
            final Signature s = new EdDSAEngine(MessageDigest.getInstance(spec.getHashAlgorithm()));
            s.initSign(privateKey);
            s.update(data.getBytes(StandardCharsets.UTF_8));
            byte[] signature = s.sign();

            String signatureString = Base64.getUrlEncoder().encodeToString(signature);

            // Print Token
            String jwt = data + "." + signatureString;
            return jwt;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        getJwt();
    }

    @Override
    public ArrayList<CityInfoDTO> selectAllCityInfo() {
        return setWeatherMapper.selectAllCityInfo();
    }

    @Override
    public List<WeatherMsg> getCityWeather(String city) {
        Date date = new Date();
        SimpleDateFormat month = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String tableNameTime = month.format(date);
        String time = simpleDateFormat.format(date);
        // 当前整分时间
        String currentMinute = time.substring(0, 13) + ":00";
        return setWeatherMapper.getCityWeather(city, tableNameTime, currentMinute);
    }

}