package com.bto.weather.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.weather.pojo.CityInfoDTO;
import com.bto.weather.pojo.WeatherMsg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SetWeatherMapper extends BaseMapper<WeatherMsg> {

    /**
     * 查询数据库中所有城市电站、坐标信息
     */
    @DS("slave")
    ArrayList<CityInfoDTO> selectAllCityInfo();

    /**
     * 插入天气数据
     */
    @DS("slave")
    int insertAll(@Param("tableNameTime") String tableNameTime,
                  @Param("city") String city,
                  @Param("time") String time,
                  @Param("weather") String weather,
                  @Param("temp") String temp,
                  @Param("wind") String wind,
                  @Param("windscale") String windscale,
                  @Param("windspeed") String windspeed,
                  @Param("humidity") String humidity,
                  @Param("precip") String precip,
                  @Param("pressure") String pressure,
                  @Param("cloud") String cloud,
                  @Param("dew") String dew,
                  @Param("currentTime") String currentTime,
                  @Param("wind360") String wind360);

    /**
     * 根据城市获取天气信息
     */
    @DS("slave")
    List<WeatherMsg> getCityWeather(@Param("city") String city,
                                    @Param("tableNameTime") String tableNameTime,
                                    @Param("time") String time);

    @DS("slave")
    int selectRepeatData(@Param("tableNameTime") String tableNameTime,
                         @Param("time") String time,
                         @Param("city") String city);

    @DS("slave")
    int updateRepeatData(@Param("tableNameTime") String tableNameTime,
                         @Param("city") String city,
                         @Param("time") String time,
                         @Param("weather") String weather,
                         @Param("temp") String temp,
                         @Param("wind") String wind,
                         @Param("windscale") String windscale,
                         @Param("windspeed") String windspeed,
                         @Param("humidity") String humidity,
                         @Param("precip") String precip,
                         @Param("pressure") String pressure,
                         @Param("cloud") String cloud,
                         @Param("dew") String dew,
                         @Param("currentTime") String currentTime,
                         @Param("wind360") String wind360);
}