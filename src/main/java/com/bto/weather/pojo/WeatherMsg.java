package com.bto.weather.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 天气主要信息类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeatherMsg implements Serializable {

    /**
     * 时间
     **/
    private String fxTime;

    private String time;

    /**
     * 温度
     **/
    private String temp;

    /**
     * 天气状况描述
     **/
    private String text;

    private String weather;

    /**
     * 风向360角度
     */
    private String wind360;

    /**
     * 风向
     **/
    private String windDir;

    private String wind;

    /**
     * 风力等级
     **/
    private String windScale;

    /**
     * 风速，公里/小时
     **/
    private String windSpeed;

    /**
     * 湿度
     **/
    private String humidity;

    /**
     * 当前小时累计降水量，默认单位：毫米
     **/
    private String precip;

    /**
     * 大气压强，默认单位：百帕
     **/
    private String pressure;

    /**
     * 云量，百分比数值。可能为空
     */
    private String cloud;

    /**
     * 露点温度。可能为空
     **/
    private String dew;

    /**
     * 所在城市
     **/
    private String city;

}