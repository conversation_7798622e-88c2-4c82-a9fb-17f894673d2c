<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.weather.mapper.SetWeatherMapper">
    <insert id="insertAll">
        INSERT INTO bto_weather_${tableNameTime} (city, time, weather, temp, wind, windscale, windspeed, humidity,
                                                  precip, pressure, cloud, dew, update_time, wind360)
        VALUES (#{city}, #{time}, #{weather}, #{temp}, #{wind}, #{windscale}, #{windspeed}, #{humidity},
                #{precip}, #{pressure}, #{cloud}, #{dew}, #{currentTime}, #{wind360})
    </insert>

    <update id="updateRepeatData">
        update bto_weather_${tableNameTime}
        set city=#{city},
            time=#{time},
            weather=#{weather},
            temp=#{temp},
            wind= #{wind},
            windscale=#{windscale},
            windspeed= #{windspeed},
            humidity= #{humidity},
            precip=#{precip},
            pressure=#{pressure},
            cloud=#{cloud},
            dew=#{dew},
            update_time=#{currentTime},
            wind360=#{wind360}
        where city = #{city}
          and time =#{time}
    </update>

    <select id="selectAllCityInfo" resultType="com.bto.weather.pojo.CityInfoDTO">
        SELECT `no`,
               city,
               long_lat location
        FROM bto_province_city;
    </select>

    <select id="getCityWeather" resultType="com.bto.weather.pojo.WeatherMsg">
        select *
        from bto_weather_${tableNameTime}
        where city = #{city}
          and time = #{time}
    </select>

    <select id="selectRepeatData" resultType="java.lang.Integer">
        select count(1)
        from bto_weather_${tableNameTime}
        where time = #{time}
          and city = #{city}
    </select>
</mapper>