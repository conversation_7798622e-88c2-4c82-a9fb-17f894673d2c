server:
  port: 9999
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          username: root
          password: qM2!tC8+lR
#          username: root
#          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: **********************************************************************************************************************************************************************
          url: **********************************************************************************************************************************************************************************************************
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: qM2!tC8+lR
          url: *****************************************************************************************************************************************************************************************************
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql