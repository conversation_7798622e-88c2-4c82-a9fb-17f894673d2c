# Configuration file for ITRF versions read from EOP files
#
# configuration entries have the following form:
#  eop-file-prefix  start-of-validity  end-of-validity  ITRF-version
# where:
#  eop-file-prefix
#       start of the file name that the EOP file should match
# start-of-validity
#       calendar date in the format yyyy-mm-dd, or a string of --- to denote past infinity
#       this date is considered INCLUDED in the validity range
# end-of-validity
#       calendar date in the format yyyy-mm-dd, or a string of --- to denote future infinity
#       this date is considered EXCLUDED from the validity range
# ITRF-version
#       a supported ITRF version in the form ITRF-yy or ITRF-yyyy
#
# empty lines or lines with '#' as their first non-blank character are ignored
# Files are matched against the lines in this file in the order they appear here, so more
# general prefixes should appear later in the file.

# EOP C04 files retrieved from the old Paris observatory ftp site and from the
# https://datacenter.iers.org/products/eop/long-term/IERS Frankfurt datacenter version embed
# in the file name. The EOP C04 files retrieved from the https://hpiers.obspm.fr/eoppc/eop/
# Paris observatory IERS site do NOT have the ITRF version embedded in the file name
# so the following entries therefore are useless anymore them.
# Starting with Orekit 12.0, this is however not a problem for .txt files because the
# ITRF version is parsed from the file header, not the file name anymore.
# The .csv and .xml files also provided by the Frankfurt datacenter do NOT have the
# ITRF version in the header.
eopc04_05.                        ------           ------          ITRF-2005
eopc04_05_IAU2000.                ------           ------          ITRF-2005
eopc04_08.                        ------           ------          ITRF-2008
eopc04_08_IAU2000.                ------           ------          ITRF-2008
eopc04_14.                        ------           ------          ITRF-2014
eopc04_14_IAU2000.                ------           ------          ITRF-2014
eopc04_20.                        ------           ------          ITRF-2020
eopc04_20_IAU2000.                ------           ------          ITRF-2020

# Bulletin A files switches between different version of ITRF have been
# extracted from from ftp://maia.usno.navy.mil/ser7/archive.notes
# the switch dates correspond to the first day of the rapid service data
# for the bulletin A that adopted the change, which is exactly 6 days earlier
bulletina-                        ------         2007-02-02        ITRF-2000
bulletina-                      2007-02-02       2011-01-28        ITRF-2005
bulletina-                      2011-01-28       2018-03-23        ITRF-2008
bulletina-                      2018-03-23         -----           ITRF-2014

# old Bulletin B still used ITRF 2005 (probably)
bulletinb_IAU1980-                ------           ------          ITRF-2005
bulletinb_IAU2000-                ------           ------          ITRF-2005

# According to Bulletins B 276 to 361, ITRF-2008 was adopted starting 2011-02-01
# According to Bulletins B 352 and later, ITRF-2014 was adopted starting 2017-03-01
bulletinb-                        ------         2011-02-01        ITRF-2005
bulletinb-                      2011-02-01       2018-03-01        ITRF-2008
bulletinb-                      2018-03-01         ------          ITRF-2014
bulletinb.                        ------         2011-02-01        ITRF-2005
bulletinb.                      2011-02-01       2017-03-01        ITRF-2008
bulletinb.                      2017-03-01         ------          ITRF-2014

# rapid data file contain Bulletin A and Bulletin B
# and Orekit extract the Bulletin B part, so we use a similar configuration
finals.                           ------         2011-02-01        ITRF-2005
finals.                         2011-02-01       2018-03-01        ITRF-2008
finals.                         2018-03-01         ------          ITRF-2014
finals2000A.                      ------         2011-02-01        ITRF-2005
finals2000A.                    2011-02-01       2017-03-01        ITRF-2008
finals2000A.                    2017-03-01         ------          ITRF-2014
